<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Aliziodev\LaravelTaxonomy\Models\Taxonomy;
use Aliziodev\LaravelTaxonomy\Models\Term;

class VerifyTaxonomyBackup extends Command
{
    protected $signature = 'backup:verify-taxonomy';
    protected $description = 'Verify taxonomy data integrity in backups';

    public function handle(): int
    {
        $this->info('Verifying taxonomy data for backup...');

        $taxonomyCount = Taxonomy::count();
        $termCount = Term::count();
        $termableCount = DB::table('termables')->count();

        $this->table(['Component', 'Count'], [
            ['Taxonomies', $taxonomyCount],
            ['Terms', $termCount],
            ['Term Relationships', $termableCount],
        ]);

        // Verify critical taxonomies exist
        $criticalTaxonomies = ['Genres', 'Moods', 'Themes'];
        foreach ($criticalTaxonomies as $taxonomyName) {
            $taxonomy = Taxonomy::where('name', $taxonomyName)->first();
            if ($taxonomy) {
                $this->line("✓ {$taxonomyName} taxonomy found with {$taxonomy->terms()->count()} terms");
            } else {
                $this->error("✗ {$taxonomyName} taxonomy missing!");
                return 1;
            }
        }

        $this->info('Taxonomy data verification completed successfully!');
        return 0;
    }
}
