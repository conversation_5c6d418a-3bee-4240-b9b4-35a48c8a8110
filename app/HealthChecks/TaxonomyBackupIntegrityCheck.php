<?php

declare(strict_types=1);

namespace App\HealthChecks;

use Spatie\Backup\Tasks\Monitor\HealthCheck;
use Spatie\Backup\BackupDestination\BackupDestination;
use Aliziodev\LaravelTaxonomy\Models\Taxonomy;
use Aliziodev\LaravelTaxonomy\Models\Term;

class TaxonomyBackupIntegrityCheck extends HealthCheck
{
    public function checkHealth(BackupDestination $backupDestination): void
    {
        $newestBackup = $backupDestination->newestBackup();

        if (!$newestBackup->exists()) {
            $this->fail('Backup file does not exist');
            return;
        }

        // Verify taxonomy data is present in current database
        $taxonomyCount = Taxonomy::count();
        $termCount = Term::count();

        if ($taxonomyCount === 0) {
            $this->fail('No taxonomies found in database - backup may be incomplete');
            return;
        }

        if ($termCount === 0) {
            $this->fail('No terms found in database - backup may be incomplete');
            return;
        }

        // Verify backup size is reasonable for taxonomy data
        $expectedMinSize = ($taxonomyCount + $termCount) * 1024; // Rough estimate
        if ($newestBackup->size() < $expectedMinSize) {
            $this->fail("Backup size ({$newestBackup->size()} bytes) seems too small for taxonomy data");
            return;
        }

        $this->pass("Backup contains {$taxonomyCount} taxonomies and {$termCount} terms");
    }
}
