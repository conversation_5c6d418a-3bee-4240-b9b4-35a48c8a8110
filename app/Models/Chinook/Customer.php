<?php

declare(strict_types=1);

namespace App\Models\Chinook;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Customer extends BaseModel
{
    protected $table = 'chinook_customers';

    protected $fillable = [
        'first_name',
        'last_name',
        'company',
        'address',
        'city',
        'state',
        'country',
        'postal_code',
        'phone',
        'fax',
        'email',
        'support_rep_id',
        'public_id',
        'slug',
        'date_of_birth',
        'preferred_language',
        'marketing_consent',
    ];

    /**
     * Customer belongs to a support representative (employee)
     */
    public function supportRep(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'support_rep_id');
    }

    /**
     * Customer has many invoices
     */
    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class, 'customer_id');
    }

    /**
     * Get full name attribute
     */
    public function getFullNameAttribute(): string
    {
        return mb_trim($this->first_name.' '.$this->last_name);
    }

    /**
     * Get route key name for URL generation
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    protected function casts(): array
    {
        return array_merge(parent::casts(), [
            'date_of_birth' => 'date',
            'marketing_consent' => 'boolean',
        ]);
    }
}
