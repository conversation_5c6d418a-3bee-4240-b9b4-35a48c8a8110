<?php

declare(strict_types=1);

namespace App\Models\Chinook;

use Aliziodev\LaravelTaxonomy\Traits\HasTaxonomy;
use App\Traits\HasSecondaryUniqueKey;
use Dyrynda\Database\Support\CascadeSoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Mattiverse\Userstamps\Traits\Userstamps;
use Override;
use Spatie\Comments\Models\Concerns\HasComments;
use Spatie\DeletedModels\Models\Concerns\KeepsDeletedModels;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;

abstract class BaseModel extends Model
{
    use CascadeSoftDeletes;
    use HasComments;
    use HasFactory;
    use HasSecondaryUniqueKey;
    use HasSlug;
    use HasTaxonomy;
    use KeepsDeletedModels {
        SoftDeletes::restore insteadof KeepsDeletedModels;
        SoftDeletes::restoreQuietly insteadof KeepsDeletedModels;
        KeepsDeletedModels::restore as keepsDeletedModelsRestore;
        KeepsDeletedModels::restoreQuietly as keepsDeletedModelsRestoreQuietly;
    }
    use SoftDeletes;
    use Userstamps;

    /*
     * This string will be used in notifications on what a new comment
     * was made.
     */
    final public function commentableName(): string
    {
        return $this->public_id;
    }

    /*
     * This URL will be used in notifications to let the user know
     * where the comment itself can be read.
     */
    #[Override]
    final public function commentUrl(): string
    {
        return $this->slug;
    }

    /**
     * Configure slug generation from public_id
     */
    final public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('public_id')
            ->saveSlugsTo('slug')
            ->doNotGenerateSlugsOnUpdate();
    }

    /**
     * Configure secondary unique key generation
     */
    final public function getSecondaryUniqueKeyOptions(): array
    {
        return [
            'field' => 'public_id',
            'type' => 'ulid', // or 'uuid', 'snowflake'
        ];
    }

    /**
     * Modern Laravel 12 casting using casts() method
     */
    protected function casts(): array
    {
        return [
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }
}
