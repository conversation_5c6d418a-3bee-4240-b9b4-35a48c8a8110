<?php

declare(strict_types=1);

namespace App\Models\Chinook;

use Illuminate\Database\Eloquent\Attributes\Scope;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Track extends BaseModel
{
    protected $table = 'chinook_tracks';

    protected $fillable = [
        'name',
        'album_id',
        'media_type_id',
        'public_id',
        'slug',
        'composer',
        'milliseconds',
        'bytes',
        'unit_price',
        'track_number',
        'disc_number',
        'lyrics',
        'isrc',
        'explicit_content',
    ];

    /**
     * Track belongs to an album
     */
    public function album(): BelongsTo
    {
        return $this->belongsTo(Album::class, 'album_id');
    }

    /**
     * Track belongs to a media type
     */
    public function mediaType(): BelongsTo
    {
        return $this->belongsTo(MediaType::class, 'media_type_id');
    }

    /**
     * Track belongs to many playlists
     */
    public function playlists(): BelongsToMany
    {
        return $this->belongsToMany(Playlist::class, 'chinook_playlist_track', 'track_id', 'playlist_id')
            ->withPivot('position')
            ->withTimestamps();
    }

    /**
     * Track has many invoice lines
     */
    public function invoiceLines(): HasMany
    {
        return $this->hasMany(InvoiceLine::class, 'track_id');
    }

    /**
     * Get artist through album relationship
     */
    public function artist()
    {
        return $this->hasOneThrough(Artist::class, Album::class, 'id', 'id', 'album_id', 'artist_id');
    }

    /**
     * Get formatted duration
     */
    public function getFormattedDurationAttribute(): string
    {
        $totalSeconds = floor($this->milliseconds / 1000);
        $minutes = floor($totalSeconds / 60);
        $seconds = $totalSeconds % 60;

        return sprintf('%d:%02d', $minutes, $seconds);
    }

    /**
     * Get formatted file size
     */
    public function getFormattedSizeAttribute(): string
    {
        if ($this->bytes < 1024) {
            return $this->bytes.' B';
        }
        if ($this->bytes < 1048576) {
            return round($this->bytes / 1024, 2).' KB';
        }

        return round($this->bytes / 1048576, 2).' MB';

    }

    /**
     * Get a route key name for URL generation
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Modern Laravel 12 casting using casts() method
     */
    protected function casts(): array
    {
        return array_merge(parent::casts(), [
            'milliseconds' => 'integer',
            'bytes' => 'integer',
            'unit_price' => 'decimal:2',
            'track_number' => 'integer',
            'disc_number' => 'integer',
            'explicit_content' => 'boolean',
        ]);
    }

    /**
     * Scope for tracks with specific taxonomy terms
     */
    #[Scope]
    protected function withTaxonomyTerm($query, string $termSlug)
    {
        return $query->whereHas('taxonomies.terms', function ($q) use ($termSlug) {
            $q->where('slug', $termSlug);
        });
    }
}
