<?php

declare(strict_types=1);

namespace App\Models\Chinook;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Employee extends BaseModel
{
    protected $table = 'chinook_employees';

    protected $fillable = [
        'last_name',
        'first_name',
        'title',
        'reports_to',
        'birth_date',
        'hire_date',
        'address',
        'city',
        'state',
        'country',
        'postal_code',
        'phone',
        'fax',
        'email',
        'public_id',
        'slug',
        'department',
        'salary',
        'is_active',
    ];

    /**
     * Employee reports to another employee (manager)
     */
    public function manager(): BelongsTo
    {
        return $this->belongsTo(self::class, 'reports_to');
    }

    /**
     * Employee has many subordinates
     */
    public function subordinates(): HasMany
    {
        return $this->hasMany(self::class, 'reports_to');
    }

    /**
     * Employee supports many customers
     */
    public function customers(): Has<PERSON>any
    {
        return $this->hasMany(Customer::class, 'support_rep_id');
    }

    /**
     * Get full name attribute
     */
    public function getFullNameAttribute(): string
    {
        return mb_trim($this->first_name.' '.$this->last_name);
    }

    /**
     * Get route key name for URL generation
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    protected function casts(): array
    {
        return array_merge(parent::casts(), [
            'birth_date' => 'date',
            'hire_date' => 'date',
            'salary' => 'integer',
            'is_active' => 'boolean',
        ]);
    }
}
