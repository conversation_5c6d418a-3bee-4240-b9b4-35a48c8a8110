<?php

declare(strict_types=1);

namespace App\Models\Chinook;

use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Playlist extends BaseModel
{
    protected $table = 'chinook_playlists';

    protected $fillable = [
        'name',
        'public_id',
        'slug',
        'description',
        'is_public',
        'total_duration',
        'track_count',
    ];

    /**
     * Playlist belongs to many tracks
     */
    public function tracks(): BelongsToMany
    {
        return $this->belongsToMany(Track::class, 'chinook_playlist_track', 'playlist_id', 'track_id')
            ->withPivot('position')
            ->withTimestamps()
            ->orderBy('pivot_position');
    }

    /**
     * Get formatted duration
     */
    public function getFormattedDurationAttribute(): string
    {
        $totalSeconds = floor($this->total_duration / 1000);
        $hours = floor($totalSeconds / 3600);
        $minutes = floor(($totalSeconds % 3600) / 60);
        $seconds = $totalSeconds % 60;

        if ($hours > 0) {
            return sprintf('%d:%02d:%02d', $hours, $minutes, $seconds);
        }

        return sprintf('%d:%02d', $minutes, $seconds);
    }

    /**
     * Get route key name for URL generation
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Modern Laravel 12 casting using casts() method
     */
    protected function casts(): array
    {
        return array_merge(parent::casts(), [
            'is_public' => 'boolean',
            'total_duration' => 'integer',
            'track_count' => 'integer',
        ]);
    }
}
