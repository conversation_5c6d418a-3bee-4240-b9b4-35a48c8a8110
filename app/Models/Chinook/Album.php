<?php

declare(strict_types=1);

namespace App\Models\Chinook;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

use function sprintf;

class Album extends BaseModel
{
    protected $table = 'chinook_albums';

    protected $fillable = [
        'title',
        'artist_id',
        'public_id',
        'slug',
        'release_date',
        'label',
        'catalog_number',
        'total_tracks',
        'duration_seconds',
        'description',
        'is_compilation',
    ];

    /**
     * Album belongs to an artist
     */
    public function artist(): BelongsTo
    {
        return $this->belongsTo(Artist::class, 'artist_id');
    }

    /**
     * Album has many tracks
     */
    public function tracks(): HasMany
    {
        return $this->hasMany(Track::class, 'album_id');
    }

    /**
     * Get formatted duration
     */
    public function getFormattedDurationAttribute(): string
    {
        $minutes = floor($this->duration_seconds / 60);
        $seconds = $this->duration_seconds % 60;

        return sprintf('%d:%02d', $minutes, $seconds);
    }

    /**
     * Get a route key name for URL generation
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * @return array
     */
    protected function casts(): array
    {
        return array_merge(parent::casts(), [
            'release_date' => 'date',
            'total_tracks' => 'integer',
            'duration_seconds' => 'integer',
            'is_compilation' => 'boolean',
        ]);
    }
}
