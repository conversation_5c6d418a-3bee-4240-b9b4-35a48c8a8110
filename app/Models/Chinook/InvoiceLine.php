<?php

declare(strict_types=1);

namespace App\Models\Chinook;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InvoiceLine extends BaseModel
{
    protected $table = 'chinook_invoice_lines';

    protected $fillable = [
        'invoice_id',
        'track_id',
        'unit_price',
        'quantity',
        'public_id',
        'slug',
        'discount_percentage',
        'line_total',
    ];

    /**
     * Invoice line belongs to an invoice
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class, 'invoice_id');
    }

    /**
     * Invoice line belongs to a track
     */
    public function track(): BelongsTo
    {
        return $this->belongsTo(Track::class, 'track_id');
    }

    /**
     * Get route key name for URL generation
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Calculate line total automatically
     */
    protected static function boot()
    {
        parent::boot();

        self::saving(function ($invoiceLine) {
            $subtotal = $invoiceLine->unit_price * $invoiceLine->quantity;
            $discount = $subtotal * ($invoiceLine->discount_percentage / 100);
            $invoiceLine->line_total = $subtotal - $discount;
        });
    }

    protected function casts(): array
    {
        return array_merge(parent::casts(), [
            'unit_price' => 'integer',
            'quantity' => 'integer',
            'discount_percentage' => 'decimal:2',
            'line_total' => 'decimal:2',
        ]);
    }
}
