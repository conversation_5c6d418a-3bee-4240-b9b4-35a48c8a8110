<?php

declare(strict_types=1);

namespace App\Models\Chinook;

use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 *
 */
class MediaType extends BaseModel
{
    protected $table = 'chinook_media_types';

    protected $fillable = [
        'name',
        'public_id',
        'slug',
        'mime_type',
        'file_extension',
        'description',
        'is_active',
    ];

    /**
     * Media type has many tracks
     */
    public function tracks(): HasMany
    {
        return $this->hasMany(Track::class, 'media_type_id');
    }

    /**
     * Get a route key name for URL generation
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    protected function casts(): array
    {
        return array_merge(parent::casts(), [
            'is_active' => 'boolean',
        ]);
    }
}
