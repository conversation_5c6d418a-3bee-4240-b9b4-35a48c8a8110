<?php

declare(strict_types=1);

namespace App\Models\Chinook;

use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Genre Model - Preserved for compatibility
 *
 * This model is maintained for data export/import compatibility
 * with the original Chinook database structure. For new implementations,
 * use the aliziodev/laravel-taxonomy package exclusively.
 */
class Genre extends BaseModel
{
    protected $table = 'chinook_genres';

    protected $fillable = [
        'name',
        'public_id',
        'slug',
        'description',
        'is_active',
    ];

    /**
     * Genre has many tracks (legacy relationship)
     *
     * @deprecated Use taxonomy relationships instead
     */
    public function tracks(): HasMany
    {
        return $this->hasMany(Track::class, 'genre_id');
    }

    /**
     * Get corresponding taxonomy term
     */
    public function getTaxonomyTermAttribute()
    {
        return \Aliziodev\LaravelTaxonomy\Models\TaxonomyTerm::where('name', $this->name)->first();
    }

    /**
     * Get a route key name for URL generation
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Modern Laravel 12 casting using casts() method
     */
    protected function casts(): array
    {
        return array_merge(parent::casts(), [
            'is_active' => 'boolean',
        ]);
    }
}
