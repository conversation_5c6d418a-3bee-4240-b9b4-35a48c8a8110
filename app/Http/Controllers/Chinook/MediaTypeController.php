<?php

namespace App\Http\Controllers\Chinook;

use App\Http\Controllers\Controller;
use App\Http\Requests\Chinook\StoreMediaTypeRequest;
use App\Http\Requests\Chinook\UpdateMediaTypeRequest;
use App\Models\Chinook\MediaType;

class MediaTypeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreMediaTypeRequest $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(MediaType $mediaType)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(MediaType $mediaType)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateMediaTypeRequest $request, MediaType $mediaType)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(MediaType $mediaType)
    {
        //
    }
}
