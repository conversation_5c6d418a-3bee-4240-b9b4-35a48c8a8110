<?php

namespace App\Http\Controllers\Chinook;

use App\Http\Controllers\Controller;
use App\Http\Requests\Chinook\StoreInvoiceLineRequest;
use App\Http\Requests\Chinook\UpdateInvoiceLineRequest;
use App\Models\Chinook\InvoiceLine;

class InvoiceLineController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreInvoiceLineRequest $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(InvoiceLine $invoiceLine)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(InvoiceLine $invoiceLine)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateInvoiceLineRequest $request, InvoiceLine $invoiceLine)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(InvoiceLine $invoiceLine)
    {
        //
    }
}
