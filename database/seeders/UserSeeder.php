<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

final class UserSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        User::factory()->create([
            'name' => 'System User',
            'email' => '<EMAIL>',
        ]);

        User::factory()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
        ]);

        User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        User::factory(10)->create();
    }
}
