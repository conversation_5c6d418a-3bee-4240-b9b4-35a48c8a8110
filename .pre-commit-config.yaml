repos:
  - repo: local
    hooks:
      - id: phpstan
        name: PHPStan
        description: 'Runs PHPStan static analysis on PHP files'
        entry: vendor/bin/phpstan analyze
        language: system
        pass_filenames: false
        files: '^(app|bin|bootstrap|config|database|routes|tests|packages|plugins)/.+\.php$'
        exclude: '^(vendor|node_modules|storage|bootstrap/cache|public|database/migrations|plugins/.*/database/migrations|reports/rector/cache)/.*$'

      - id: rector
        name: Rector Type Safety
        description: 'Checks for type safety issues with <PERSON>'
        entry: vendor/bin/rector process --dry-run --config=rector-type-safety.php
        language: system
        pass_filenames: false
        files: '^(app|bin|bootstrap|config|database|routes|tests|packages|plugins)/.+\.php$'
        exclude: '^(vendor|node_modules|storage|bootstrap/cache|public|database/migrations|plugins/.*/database/migrations|reports/rector/cache)/.*$'

      - id: pint
        name: Laravel Pint
        description: 'Runs Laravel Pint PHP code style fixer'
        entry: vendor/bin/pint --test
        language: system
        pass_filenames: false
        files: '^(app|bin|bootstrap|config|database|routes|tests|packages|plugins)/.+\.php$'
        exclude: '^(vendor|node_modules|storage|bootstrap/cache|public|database/migrations|plugins/.*/database/migrations|reports/rector/cache)/.*$'

      - id: typescript-check
        name: TypeScript Check
        description: 'Runs TypeScript compiler for type checking'
        entry: pnpm tsc --noEmit
        language: system
        pass_filenames: false
        files: '\.(ts|tsx)$'
        exclude: '^(node_modules|vendor|public|storage|dist|build)/.*$'

      - id: eslint
        name: ESLint
        description: 'Runs ESLint for TypeScript files'
        entry: pnpm eslint
        language: system
        args: ['--ext', '.ts,.tsx', 'resources/js']
        pass_filenames: false
        files: '\.(ts|tsx)$'
        exclude: '^(node_modules|vendor|public|storage|dist|build)/.*$'
