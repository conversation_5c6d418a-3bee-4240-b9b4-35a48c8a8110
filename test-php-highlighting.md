# PhpStorm PHP Syntax Highlighting Test

This file tests PHP syntax highlighting in PhpStorm Markdown files.

## Test 1: Basic PHP Code Block

```php
<?php

declare(strict_types=1);

namespace App\Test;

use Illuminate\Database\Eloquent\Model;

class TestModel extends Model
{
    protected $table = 'test_models';
    
    protected $fillable = ['name', 'active'];
    
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }
}
```

## Test 2: XML Code Block (Should Work)

```xml
<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="MarkdownSettings">
    <option name="injectLanguagesInCodeFences" value="false" />
  </component>
</project>
```

## Test 3: JavaScript Code Block (Should Work)

```javascript
function testFunction() {
    const message = "Hello World";
    console.log(message);
    return { success: true };
}
```

## Expected Results for PHP Block

If working correctly, you should see:
- `<?php`, `declare`, `namespace`, `class`, `function`, `return` highlighted as keywords
- `$table`, `$fillable`, `$query` highlighted as variables
- `'test_models'`, `'name'`, `'active'`, `true` highlighted as values
- `// comments` and `/** docblocks */` styled differently
- Method calls like `->where()` properly colored
