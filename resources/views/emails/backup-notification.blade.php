@component('mail::message')
    # Chinook Backup {{ $event }}

    **Application:** {{ config('app.name') }}
    **Environment:** {{ app()->environment() }}
    **Time:** {{ now()->format('Y-m-d H:i:s T') }}

    @if($event === 'successful')
        **Backup Details:**
        - **Size:** {{ $backupDestination->newestBackup()->size() }}
        - **Destination:** {{ $backupDestination->diskName() }}
        - **Taxonomies:** {{ \Aliziodev\LaravelTaxonomy\Models\Taxonomy::count() }} taxonomies
        - **Terms:** {{ \Aliziodev\LaravelTaxonomy\Models\Term::count() }} terms
        - **Relationships:** {{ DB::table('termables')->count() }} term relationships
    @endif

    @if($event === 'failed')
        @component('mail::panel')
            **Error Details:**
            {{ $exception->getMessage() }}

            **Taxonomy Status:**
            - Taxonomies: {{ \Aliziodev\LaravelTaxonomy\Models\Taxonomy::count() }}
            - Terms: {{ \Aliziodev\LaravelTaxonomy\Models\Term::count() }}
        @endcomponent
    @endif

    Thanks,<br>
    {{ config('app.name') }} Backup System
@endcomponent
