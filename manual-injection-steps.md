# Manual Language Injection Configuration

If the automatic configuration doesn't work, follow these steps:

## Method 1: UI-Based Configuration

1. **Open Settings**: File → Settings → Editor → Language Injections
2. **Add New Injection**: Click the "+" button
3. **Configure Injection**:
   - **ID**: `markdown-php-injection`
   - **Language**: Select "PHP" from dropdown
   - **Places Patterns**: Click "+" to add a pattern
   - **File Type**: Select "Markdown"
   - **Element Pattern**: Enter this exact pattern:
     ```
     psi().inside(psi().withElementType("MARKDOWN_CODE_FENCE_CONTENT").withParent(psi().withElementType("MARKDOWN_CODE_FENCE").withChild(psi().withElementType("MARKDOWN_CODE_FENCE_START").withText(string().startsWith("```php")))))
     ```
4. **Apply and OK**: Save the settings
5. **Restart PhpStorm**: Complete restart required

## Method 2: Quick Injection (Temporary)

1. Open any Markdown file with PHP code blocks
2. Place cursor inside a ```php code block
3. Press Alt+Enter (Option+Enter on macOS)
4. Select "Inject language or reference"
5. Choose "PHP" from the list
6. PhpStorm will remember this for the session

## Verification Steps

After configuration:
1. Open test-php-highlighting.md
2. Check PHP code block for syntax highlighting
3. Verify XML and JavaScript blocks still work
4. Test code completion by typing in PHP block
