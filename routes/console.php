<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

Schedule::command('activitylog:clean')->daily();

// Daily full backup at 2 AM (includes taxonomy data)
Schedule::command('backup:run')
    ->daily()
    ->at('02:00')
    ->environments(['production', 'staging'])
    ->before(function () {
        // Pre-backup taxonomy verification
        Artisan::call('backup:verify-taxonomy');
    })
    ->name('daily-backup')
    ->description('Daily full backup including taxonomy data');

// Weekly backup cleanup (keep last 4 weeks)
Schedule::command('backup:clean')
    ->weekly()
    ->sundays()
    ->at('03:00')
    ->environments(['production', 'staging'])
    ->name('weekly-cleanup')
    ->description('Weekly backup cleanup');

// Daily backup monitoring with taxonomy health check
Schedule::command('backup:monitor')
    ->daily()
    ->at('08:00')
    ->environments(['production', 'staging'])
    ->after(function () {
        // Post-backup taxonomy integrity check
        Artisan::call('backup:verify-taxonomy');
    })
    ->name('backup-monitor')
    ->description('Daily backup monitoring with taxonomy health check');

Schedule::command('model:prune', [
    '--model' => [\Spatie\DeletedModels\Models\DeletedModel::class],
])->daily();
