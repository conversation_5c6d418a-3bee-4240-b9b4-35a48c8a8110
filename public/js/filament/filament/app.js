(()=>{var ee=Object.create,q=Object.defineProperty,te=Object.getPrototypeOf,re=Object.prototype.hasOwnProperty,ne=Object.getOwnPropertyNames,ie=Object.getOwnPropertyDescriptor,ae=r=>q(r,"__esModule",{value:!0}),se=(r,n)=>()=>(n||(n={exports:{}},r(n.exports,n)),n.exports),oe=(r,n,a)=>{if(n&&typeof n=="object"||typeof n=="function")for(let f of ne(n))!re.call(r,f)&&f!=="default"&&q(r,f,{get:()=>n[f],enumerable:!(a=ie(n,f))||a.enumerable});return r},fe=r=>oe(ae(q(r!=null?ee(te(r)):{},"default",r&&r.__esModule&&"default"in r?{get:()=>r.default,enumerable:!0}:{value:r,enumerable:!0})),r),le=se((r,n)=>{(function(a,f,g){if(!a)return;for(var c={8:"backspace",9:"tab",13:"enter",16:"shift",17:"ctrl",18:"alt",20:"capslock",27:"esc",32:"space",33:"pageup",34:"pagedown",35:"end",36:"home",37:"left",38:"up",39:"right",40:"down",45:"ins",46:"del",91:"meta",93:"meta",224:"meta"},w={106:"*",107:"+",109:"-",110:".",111:"/",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},y={"~":"`","!":"1","@":"2","#":"3",$:"4","%":"5","^":"6","&":"7","*":"8","(":"9",")":"0",_:"-","+":"=",":":";",'"':"'","<":",",">":".","?":"/","|":"\\"},x={option:"alt",command:"meta",return:"enter",escape:"esc",plus:"+",mod:/Mac|iPod|iPhone|iPad/.test(navigator.platform)?"meta":"ctrl"},P,b=1;b<20;++b)c[111+b]="f"+b;for(b=0;b<=9;++b)c[b+96]=b.toString();function S(e,t,s){if(e.addEventListener){e.addEventListener(t,s,!1);return}e.attachEvent("on"+t,s)}function G(e){if(e.type=="keypress"){var t=String.fromCharCode(e.which);return e.shiftKey||(t=t.toLowerCase()),t}return c[e.which]?c[e.which]:w[e.which]?w[e.which]:String.fromCharCode(e.which).toLowerCase()}function R(e,t){return e.sort().join(",")===t.sort().join(",")}function H(e){var t=[];return e.shiftKey&&t.push("shift"),e.altKey&&t.push("alt"),e.ctrlKey&&t.push("ctrl"),e.metaKey&&t.push("meta"),t}function z(e){if(e.preventDefault){e.preventDefault();return}e.returnValue=!1}function F(e){if(e.stopPropagation){e.stopPropagation();return}e.cancelBubble=!0}function C(e){return e=="shift"||e=="ctrl"||e=="alt"||e=="meta"}function J(){if(!P){P={};for(var e in c)e>95&&e<112||c.hasOwnProperty(e)&&(P[c[e]]=e)}return P}function B(e,t,s){return s||(s=J()[e]?"keydown":"keypress"),s=="keypress"&&t.length&&(s="keydown"),s}function X(e){return e==="+"?["+"]:(e=e.replace(/\+{2}/g,"+plus"),e.split("+"))}function T(e,t){var s,h,k,A=[];for(s=X(e),k=0;k<s.length;++k)h=s[k],x[h]&&(h=x[h]),t&&t!="keypress"&&y[h]&&(h=y[h],A.push("shift")),C(h)&&A.push(h);return t=B(h,A,t),{key:h,modifiers:A,action:t}}function I(e,t){return e===null||e===f?!1:e===t?!0:I(e.parentNode,t)}function v(e){var t=this;if(e=e||f,!(t instanceof v))return new v(e);t.target=e,t._callbacks={},t._directMap={};var s={},h,k=!1,A=!1,D=!1;function E(i){i=i||{};var l=!1,p;for(p in s){if(i[p]){l=!0;continue}s[p]=0}l||(D=!1)}function U(i,l,p,o,d,m){var u,_,M=[],O=p.type;if(!t._callbacks[i])return[];for(O=="keyup"&&C(i)&&(l=[i]),u=0;u<t._callbacks[i].length;++u)if(_=t._callbacks[i][u],!(!o&&_.seq&&s[_.seq]!=_.level)&&O==_.action&&(O=="keypress"&&!p.metaKey&&!p.ctrlKey||R(l,_.modifiers))){var Z=!o&&_.combo==d,$=o&&_.seq==o&&_.level==m;(Z||$)&&t._callbacks[i].splice(u,1),M.push(_)}return M}function L(i,l,p,o){t.stopCallback(l,l.target||l.srcElement,p,o)||i(l,p)===!1&&(z(l),F(l))}t._handleKey=function(i,l,p){var o=U(i,l,p),d,m={},u=0,_=!1;for(d=0;d<o.length;++d)o[d].seq&&(u=Math.max(u,o[d].level));for(d=0;d<o.length;++d){if(o[d].seq){if(o[d].level!=u)continue;_=!0,m[o[d].seq]=1,L(o[d].callback,p,o[d].combo,o[d].seq);continue}_||L(o[d].callback,p,o[d].combo)}var M=p.type=="keypress"&&A;p.type==D&&!C(i)&&!M&&E(m),A=_&&p.type=="keydown"};function K(i){typeof i.which!="number"&&(i.which=i.keyCode);var l=G(i);if(l){if(i.type=="keyup"&&k===l){k=!1;return}t.handleKey(l,H(i),i)}}function Y(){clearTimeout(h),h=setTimeout(E,1e3)}function Q(i,l,p,o){s[i]=0;function d(O){return function(){D=O,++s[i],Y()}}function m(O){L(p,O,i),o!=="keyup"&&(k=G(O)),setTimeout(E,10)}for(var u=0;u<l.length;++u){var _=u+1===l.length,M=_?m:d(o||T(l[u+1]).action);W(l[u],M,o,i,u)}}function W(i,l,p,o,d){t._directMap[i+":"+p]=l,i=i.replace(/\s+/g," ");var m=i.split(" "),u;if(m.length>1){Q(i,m,l,p);return}u=T(i,p),t._callbacks[u.key]=t._callbacks[u.key]||[],U(u.key,u.modifiers,{type:u.action},o,i,d),t._callbacks[u.key][o?"unshift":"push"]({callback:l,modifiers:u.modifiers,action:u.action,seq:o,level:d,combo:i})}t._bindMultiple=function(i,l,p){for(var o=0;o<i.length;++o)W(i[o],l,p)},S(e,"keypress",K),S(e,"keydown",K),S(e,"keyup",K)}v.prototype.bind=function(e,t,s){var h=this;return e=e instanceof Array?e:[e],h._bindMultiple.call(h,e,t,s),h},v.prototype.unbind=function(e,t){var s=this;return s.bind.call(s,e,function(){},t)},v.prototype.trigger=function(e,t){var s=this;return s._directMap[e+":"+t]&&s._directMap[e+":"+t]({},e),s},v.prototype.reset=function(){var e=this;return e._callbacks={},e._directMap={},e},v.prototype.stopCallback=function(e,t){var s=this;if((" "+t.className+" ").indexOf(" mousetrap ")>-1||I(t,s.target))return!1;if("composedPath"in e&&typeof e.composedPath=="function"){var h=e.composedPath()[0];h!==e.target&&(t=h)}return t.tagName=="INPUT"||t.tagName=="SELECT"||t.tagName=="TEXTAREA"||t.isContentEditable},v.prototype.handleKey=function(){var e=this;return e._handleKey.apply(e,arguments)},v.addKeycodes=function(e){for(var t in e)e.hasOwnProperty(t)&&(c[t]=e[t]);P=null},v.init=function(){var e=v(f);for(var t in e)t.charAt(0)!=="_"&&(v[t]=function(s){return function(){return e[s].apply(e,arguments)}}(t))},v.init(),a.Mousetrap=v,typeof n<"u"&&n.exports&&(n.exports=v),typeof define=="function"&&define.amd&&define(function(){return v})})(typeof window<"u"?window:null,typeof window<"u"?document:null)}),j=fe(le());(function(r){if(r){var n={},a=r.prototype.stopCallback;r.prototype.stopCallback=function(f,g,c,w){var y=this;return y.paused?!0:n[c]||n[w]?!1:a.call(y,f,g,c)},r.prototype.bindGlobal=function(f,g,c){var w=this;if(w.bind(f,g,c),f instanceof Array){for(var y=0;y<f.length;y++)n[f[y]]=!0;return}n[f]=!0},r.init()}})(typeof Mousetrap<"u"?Mousetrap:void 0);var ue=r=>{r.directive("mousetrap",(n,{modifiers:a,expression:f},{evaluate:g})=>{let c=()=>f?g(f):n.click();a=a.map(w=>w.replace(/--/g," ").replace(/-/g,"+").replace(/\bslash\b/g,"/")),a.includes("global")&&(a=a.filter(w=>w!=="global"),j.default.bindGlobal(a,w=>{w.preventDefault(),c()})),j.default.bind(a,w=>{w.preventDefault(),c()})})},N=ue;var V=()=>({isOpen:window.Alpine.$persist(!0).as("isOpen"),isOpenDesktop:window.Alpine.$persist(!0).as("isOpenDesktop"),collapsedGroups:window.Alpine.$persist(null).as("collapsedGroups"),init(){let r=window.innerWidth;new ResizeObserver(()=>{let a=window.innerWidth,f=r>=1024,g=a<1024,c=a>=1024;f&&g?(this.isOpenDesktop=this.isOpen,this.isOpen&&this.close()):!f&&c&&(this.isOpen=this.isOpenDesktop),r=a}).observe(document.body),window.innerWidth<1024?this.isOpen&&(this.isOpenDesktop=!0,this.close()):this.isOpenDesktop=this.isOpen},groupIsCollapsed(r){return this.collapsedGroups.includes(r)},collapseGroup(r){this.collapsedGroups.includes(r)||(this.collapsedGroups=this.collapsedGroups.concat(r))},toggleCollapsedGroup(r){this.collapsedGroups=this.collapsedGroups.includes(r)?this.collapsedGroups.filter(n=>n!==r):this.collapsedGroups.concat(r)},close(){this.isOpen=!1,window.innerWidth>=1024&&(this.isOpenDesktop=!1)},open(){this.isOpen=!0,window.innerWidth>=1024&&(this.isOpenDesktop=!0)}});document.addEventListener("alpine:init",()=>{let r=localStorage.getItem("theme")??getComputedStyle(document.documentElement).getPropertyValue("--default-theme-mode");window.Alpine.store("theme",r==="dark"||r==="system"&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),window.addEventListener("theme-changed",n=>{let a=n.detail;localStorage.setItem("theme",a),a==="system"&&(a=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),window.Alpine.store("theme",a)}),window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",n=>{localStorage.getItem("theme")==="system"&&window.Alpine.store("theme",n.matches?"dark":"light")}),window.Alpine.effect(()=>{window.Alpine.store("theme")==="dark"?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark")})});document.addEventListener("DOMContentLoaded",()=>{setTimeout(()=>{let r=document.querySelector(".fi-main-sidebar .fi-sidebar-item.fi-active");if((!r||r.offsetParent===null)&&(r=document.querySelector(".fi-main-sidebar .fi-sidebar-group.fi-active")),!r||r.offsetParent===null)return;let n=document.querySelector(".fi-main-sidebar .fi-sidebar-nav");n&&n.scrollTo(0,r.offsetTop-window.innerHeight/2)},10)});window.setUpUnsavedDataChangesAlert=({body:r,livewireComponent:n,$wire:a})=>{window.addEventListener("beforeunload",f=>{window.jsMd5(JSON.stringify(a.data).replace(/\\/g,""))===a.savedDataHash||a?.__instance?.effects?.redirect||(f.preventDefault(),f.returnValue=!0)})};window.setUpSpaModeUnsavedDataChangesAlert=({body:r,resolveLivewireComponentUsing:n,$wire:a})=>{let f=()=>a?.__instance?.effects?.redirect?!1:window.jsMd5(JSON.stringify(a.data).replace(/\\/g,""))!==a.savedDataHash,g=()=>confirm(r);document.addEventListener("livewire:navigate",c=>{if(typeof n()<"u"){if(!f()||g())return;c.preventDefault()}}),window.addEventListener("beforeunload",c=>{f()&&(c.preventDefault(),c.returnValue=!0)})};window.setUpUnsavedActionChangesAlert=({resolveLivewireComponentUsing:r,$wire:n})=>{window.addEventListener("beforeunload",a=>{if(!(typeof r()>"u")&&(n.mountedActions?.length??0)&&!n?.__instance?.effects?.redirect){a.preventDefault(),a.returnValue=!0;return}})};document.addEventListener("alpine:init",()=>{window.Alpine.plugin(N),window.Alpine.store("sidebar",V())});})();
