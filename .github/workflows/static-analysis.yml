name: Static Analysis

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

jobs:
  phpstan:
    name: PHPStan Analysis
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.4'
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, bcmath, intl, gd, exif, iconv, redis
          coverage: none
          tools: composer:v2

      - name: Get composer cache directory
        id: composer-cache
        run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

      - name: Cache composer dependencies
        uses: actions/cache@v3
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: <PERSON><PERSON> PHPStan results
        uses: actions/cache@v3
        with:
          path: var/cache/phpstan
          key: ${{ runner.os }}-phpstan-${{ github.sha }}
          restore-keys: ${{ runner.os }}-phpstan-

      - name: Install dependencies
        run: composer install --prefer-dist --no-interaction --no-progress

      - name: Run PHPStan
        run: ./vendor/bin/phpstan analyse --memory-limit=1G --no-progress

      - name: Upload PHPStan results as artifact
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: phpstan-results
          path: var/cache/phpstan
